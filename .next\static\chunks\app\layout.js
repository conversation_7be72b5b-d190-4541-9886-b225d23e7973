/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AudienceContext.tsx */ \"(app-pages-browser)/./src/contexts/AudienceContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/PersonaContext.tsx */ \"(app-pages-browser)/./src/contexts/PersonaContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1QyVFNiU5NiVCMCVFNSU4NiU5OSVFNCVCRCU5QyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTYlOTYlQjAlRTUlODYlOTklRTQlQkQlOUMlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q0F1ZGllbmNlQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdWRpZW5jZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTYlOTYlQjAlRTUlODYlOTklRTQlQkQlOUMlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q1BlcnNvbmFDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlBlcnNvbmFQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFtRTtBQUNuRTtBQUNBLDhMQUFzSDtBQUN0SDtBQUNBLDRMQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2YzM2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFzmlrDlhpnkvZxcXFxcc3JjXFxcXGFwcFxcXFxnbG9iYWxzLmNzc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXVkaWVuY2VQcm92aWRlclwiXSAqLyBcIkQ6XFxcXOaWsOWGmeS9nFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXVkaWVuY2VDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUGVyc29uYVByb3ZpZGVyXCJdICovIFwiRDpcXFxc5paw5YaZ5L2cXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxQZXJzb25hQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"93544e8464ef\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZmJhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkzNTQ0ZTg0NjRlZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AudienceContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/AudienceContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudienceProvider: function() { return /* binding */ AudienceProvider; },\n/* harmony export */   audienceEventBus: function() { return /* binding */ audienceEventBus; },\n/* harmony export */   useAudience: function() { return /* binding */ useAudience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_audienceService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/audienceService */ \"(app-pages-browser)/./src/services/audienceService.ts\");\n/**\r\n * 受众全局状态管理Context\r\n * 提供受众的实时状态管理和事件通知机制\r\n */ /* __next_internal_client_entry_do_not_use__ AudienceProvider,useAudience,audienceEventBus auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Context创建\nconst AudienceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// 事件总线类\nclass AudienceEventBus {\n    subscribe(eventType, callback) {\n        if (!this.listeners.has(eventType)) {\n            this.listeners.set(eventType, []);\n        }\n        const callbacks = this.listeners.get(eventType);\n        callbacks.push(callback);\n        // 返回取消订阅函数\n        return ()=>{\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        };\n    }\n    emit(event) {\n        const callbacks = this.listeners.get(event.type) || [];\n        const allCallbacks = this.listeners.get(\"*\") || [];\n        // 触发特定类型的监听器\n        callbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"受众事件回调执行失败:\", error);\n            }\n        });\n        // 触发通用监听器\n        allCallbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"受众事件回调执行失败:\", error);\n            }\n        });\n    }\n    clear() {\n        this.listeners.clear();\n    }\n    constructor(){\n        this.listeners = new Map();\n    }\n}\n// 全局事件总线实例\nconst audienceEventBus = new AudienceEventBus();\n// Provider组件\nconst AudienceProvider = (param)=>{\n    let { children, initialAudience } = param;\n    _s();\n    const [currentAudience, setCurrentAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialAudience || null);\n    const [allAudiences, setAllAudiences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const audienceService = _services_audienceService__WEBPACK_IMPORTED_MODULE_2__.AudienceService.getInstance();\n    const initializingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 发出受众变化事件\n    const emitAudienceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type, audience, previousAudience, error)=>{\n        // 避免重复触发相同的事件\n        if (type === \"activate\" && audience && previousAudience && audience.id === previousAudience.id) {\n            return; // 如果是相同的受众，不触发事件\n        }\n        const event = {\n            type,\n            audience,\n            previousAudience,\n            error,\n            timestamp: Date.now()\n        };\n        audienceEventBus.emit(event);\n        // 只在开发环境下输出详细日志\n        if (true) {\n            console.log(\"\\uD83C\\uDFAF 受众事件:\", {\n                type,\n                audienceName: audience === null || audience === void 0 ? void 0 : audience.name,\n                error\n            });\n        }\n    }, []);\n    // 初始化加载当前激活的受众和所有受众列表\n    const initializeAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (initializingRef.current) return;\n        initializingRef.current = true;\n        setIsLoading(true);\n        setError(null);\n        const startTime = Date.now();\n        console.log(\"\\uD83D\\uDE80 开始初始化受众Context...\");\n        try {\n            var _initResult_createdAudiences;\n            // 首先尝试初始化受众系统（包含完整的错误处理和重试机制）\n            const initResult = await audienceService.initializeAudienceSystem();\n            if (!initResult.success) {\n                throw new Error(initResult.error || \"受众系统初始化失败\");\n            }\n            console.log(\"✅ 受众系统初始化成功:\", {\n                isFirstTime: initResult.isFirstTime,\n                initializationTime: initResult.initializationTime,\n                defaultAudienceId: initResult.defaultAudienceId,\n                createdAudiences: ((_initResult_createdAudiences = initResult.createdAudiences) === null || _initResult_createdAudiences === void 0 ? void 0 : _initResult_createdAudiences.length) || 0\n            });\n            // 获取当前激活的受众\n            const activeResult = await audienceService.getActiveAudience();\n            if (activeResult.success && activeResult.data) {\n                const previousAudience = currentAudience;\n                setCurrentAudience(activeResult.data);\n                emitAudienceChange(\"activate\", activeResult.data, previousAudience || undefined);\n                console.log(\"✅ 激活受众加载成功:\", activeResult.data.name);\n            } else {\n                console.warn(\"⚠️ 未找到激活的受众\");\n            }\n            // 获取所有受众列表\n            const allResult = await audienceService.getAllAudiences();\n            if (allResult.success && allResult.data) {\n                setAllAudiences(allResult.data);\n                console.log(\"✅ 受众列表加载成功，共\", allResult.data.length, \"个受众\");\n            } else {\n                console.warn(\"⚠️ 受众列表加载失败:\", allResult.error);\n            }\n            const totalTime = Date.now() - startTime;\n            console.log(\"✅ 受众Context初始化完成，总耗时:\", totalTime, \"ms\");\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"加载受众失败\";\n            console.error(\"❌ 受众Context初始化失败:\", errorMessage);\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience || undefined, errorMessage);\n            // 即使初始化失败，也尝试加载现有数据\n            try {\n                console.log(\"\\uD83D\\uDD04 尝试加载现有受众数据...\");\n                const fallbackResult = await audienceService.getAllAudiences();\n                if (fallbackResult.success && fallbackResult.data && fallbackResult.data.length > 0) {\n                    setAllAudiences(fallbackResult.data);\n                    // 尝试找到激活的受众\n                    const activeAudience = fallbackResult.data.find((audience)=>audience.isActive);\n                    if (activeAudience) {\n                        setCurrentAudience(activeAudience);\n                        console.log(\"✅ 降级模式：加载到激活受众\", activeAudience.name);\n                    }\n                }\n            } catch (fallbackError) {\n                console.error(\"❌ 降级模式也失败了:\", fallbackError);\n            }\n        } finally{\n            setIsLoading(false);\n            initializingRef.current = false;\n        }\n    }, [\n        currentAudience,\n        audienceService,\n        emitAudienceChange\n    ]);\n    // 创建受众\n    const createAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceData)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await audienceService.createAudience(audienceData);\n            if (result.success && result.data) {\n                // 更新受众列表\n                setAllAudiences((prev)=>[\n                        result.data,\n                        ...prev\n                    ]);\n                emitAudienceChange(\"create\", result.data);\n                console.log(\"✅ 受众创建成功:\", result.data.name);\n            } else {\n                throw new Error(result.error || \"创建受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"创建受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 更新受众\n    const updateAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId, updates)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await audienceService.updateAudience(audienceId, updates);\n            if (result.success) {\n                // 获取更新后的受众信息\n                const updatedResult = await audienceService.getAudience(audienceId);\n                if (updatedResult.success && updatedResult.data) {\n                    const updatedAudience = updatedResult.data;\n                    // 更新受众列表\n                    setAllAudiences((prev)=>prev.map((audience)=>audience.id === audienceId ? updatedAudience : audience));\n                    // 如果更新的是当前激活受众，也更新当前状态\n                    if (currentAudience && currentAudience.id === audienceId) {\n                        setCurrentAudience(updatedAudience);\n                    }\n                    emitAudienceChange(\"update\", updatedAudience, currentAudience);\n                    console.log(\"✅ 受众更新成功:\", audienceId);\n                }\n            } else {\n                throw new Error(result.error || \"更新受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"更新受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 删除受众\n    const deleteAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取要删除的受众信息\n            const audienceResult = await audienceService.getAudience(audienceId);\n            const audienceToDelete = audienceResult.success ? audienceResult.data : null;\n            const result = await audienceService.deleteAudience(audienceId);\n            if (result.success) {\n                // 更新受众列表\n                setAllAudiences((prev)=>prev.filter((audience)=>audience.id !== audienceId));\n                // 如果删除的是当前激活受众，清除当前状态\n                if (currentAudience && currentAudience.id === audienceId) {\n                    setCurrentAudience(null);\n                }\n                emitAudienceChange(\"delete\", audienceToDelete || undefined, currentAudience);\n                console.log(\"✅ 受众删除成功:\", audienceId);\n            } else {\n                throw new Error(result.error || \"删除受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"删除受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 激活受众\n    const activateAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取受众信息\n            const audienceResult = await audienceService.getAudience(audienceId);\n            if (!audienceResult.success || !audienceResult.data) {\n                throw new Error(\"受众不存在\");\n            }\n            // 激活受众\n            const result = await audienceService.setActiveAudience(audienceId);\n            if (result.success) {\n                const previousAudience = currentAudience;\n                setCurrentAudience(audienceResult.data);\n                // 更新受众列表中的激活状态\n                setAllAudiences((prev)=>prev.map((audience)=>({\n                            ...audience,\n                            isActive: audience.id === audienceId\n                        })));\n                emitAudienceChange(\"activate\", audienceResult.data, previousAudience);\n                console.log(\"✅ 受众激活成功:\", audienceResult.data.name);\n            } else {\n                throw new Error(result.error || \"激活受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"激活受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 刷新受众数据\n    const refreshAudiences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await initializeAudience();\n    }, [\n        initializeAudience\n    ]);\n    // 事件订阅\n    const onAudienceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((callback)=>{\n        return audienceEventBus.subscribe(\"*\", callback);\n    }, []);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!initialAudience) {\n            initializeAudience();\n        }\n        // 清理函数\n        return ()=>{\n            audienceEventBus.clear();\n        };\n    }, [\n        initialAudience,\n        initializeAudience\n    ]);\n    const contextValue = {\n        currentAudience,\n        allAudiences,\n        isLoading,\n        error,\n        createAudience,\n        updateAudience,\n        deleteAudience,\n        activateAudience,\n        refreshAudiences,\n        onAudienceChange\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AudienceContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\contexts\\\\AudienceContext.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AudienceProvider, \"i1ic6lsEhefhZYE3boNbGpNq/U0=\");\n_c = AudienceProvider;\n// Hook for using AudienceContext\nconst useAudience = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AudienceContext);\n    if (!context) {\n        throw new Error(\"useAudience must be used within an AudienceProvider\");\n    }\n    return context;\n};\n_s1(useAudience, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 导出事件总线供高级用法\n\nvar _c;\n$RefreshReg$(_c, \"AudienceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AudienceContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/PersonaContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/PersonaContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PersonaProvider: function() { return /* binding */ PersonaProvider; },\n/* harmony export */   personaEventBus: function() { return /* binding */ personaEventBus; },\n/* harmony export */   usePersona: function() { return /* binding */ usePersona; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_personaService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/personaService */ \"(app-pages-browser)/./src/services/personaService.ts\");\n/**\r\n * 人设全局状态管理Context\r\n * 提供人设的实时状态管理和事件通知机制\r\n */ /* __next_internal_client_entry_do_not_use__ PersonaProvider,usePersona,personaEventBus auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Context创建\nconst PersonaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// 事件总线类\nclass PersonaEventBus {\n    subscribe(eventType, callback) {\n        if (!this.listeners.has(eventType)) {\n            this.listeners.set(eventType, []);\n        }\n        const callbacks = this.listeners.get(eventType);\n        callbacks.push(callback);\n        // 返回取消订阅函数\n        return ()=>{\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        };\n    }\n    emit(event) {\n        const callbacks = this.listeners.get(event.type) || [];\n        const allCallbacks = this.listeners.get(\"*\") || [];\n        // 触发特定类型的监听器\n        callbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"人设事件回调执行失败:\", error);\n            }\n        });\n        // 触发通用监听器\n        allCallbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"人设事件回调执行失败:\", error);\n            }\n        });\n    }\n    clear() {\n        this.listeners.clear();\n    }\n    constructor(){\n        this.listeners = new Map();\n    }\n}\n// 全局事件总线实例\nconst personaEventBus = new PersonaEventBus();\n// Provider组件\nconst PersonaProvider = (param)=>{\n    let { children, initialPersona } = param;\n    _s();\n    const [currentPersona, setCurrentPersona] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPersona || null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const personaService = _services_personaService__WEBPACK_IMPORTED_MODULE_2__.PersonaService.getInstance();\n    const initializingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 发出人设变化事件\n    const emitPersonaChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type, persona, previousPersona, error)=>{\n        // 避免重复触发相同的事件\n        if (type === \"activate\" && persona && previousPersona && persona.id === previousPersona.id) {\n            return; // 如果是相同的人设，不触发事件\n        }\n        const event = {\n            type,\n            persona,\n            previousPersona,\n            error,\n            timestamp: Date.now()\n        };\n        personaEventBus.emit(event);\n        // 只在开发环境下输出详细日志\n        if (true) {\n            console.log(\"\\uD83C\\uDFAD 人设事件:\", {\n                type,\n                personaName: persona === null || persona === void 0 ? void 0 : persona.name,\n                error\n            });\n        }\n    }, []);\n    // 初始化加载当前激活的人设\n    const initializePersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (initializingRef.current) return;\n        initializingRef.current = true;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await personaService.getActivePersona();\n            if (result.success && result.data) {\n                const previousPersona = currentPersona;\n                setCurrentPersona(result.data);\n                emitPersonaChange(\"activate\", result.data, previousPersona);\n            } else {\n                // 如果没有激活的人设，尝试初始化系统\n                try {\n                    await personaService.initializePersonaSystem();\n                    const retryResult = await personaService.getActivePersona();\n                    if (retryResult.success && retryResult.data) {\n                        const previousPersona = currentPersona;\n                        setCurrentPersona(retryResult.data);\n                        emitPersonaChange(\"activate\", retryResult.data, previousPersona);\n                    }\n                } catch (initError) {\n                    console.warn(\"人设系统初始化失败:\", initError);\n                }\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"加载人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n        } finally{\n            setIsLoading(false);\n            initializingRef.current = false;\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 选择人设（立即生效）\n    const selectPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (persona)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 立即更新状态，提供即时反馈\n            const previousPersona = currentPersona;\n            setCurrentPersona(persona);\n            // 异步激活人设\n            const result = await personaService.setActivePersona(persona.id);\n            if (result.success) {\n                emitPersonaChange(\"select\", persona, previousPersona);\n                console.log(\"✅ 人设选择成功:\", persona.name);\n            } else {\n                // 如果激活失败，回滚状态\n                setCurrentPersona(previousPersona);\n                throw new Error(result.error || \"激活人设失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"选择人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 激活人设（通过ID）\n    const activatePersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (personaId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取人设信息\n            const personaResult = await personaService.getPersona(personaId);\n            if (!personaResult.success || !personaResult.data) {\n                throw new Error(\"人设不存在\");\n            }\n            // 激活人设\n            const result = await personaService.setActivePersona(personaId);\n            if (result.success) {\n                const previousPersona = currentPersona;\n                setCurrentPersona(personaResult.data);\n                emitPersonaChange(\"activate\", personaResult.data, previousPersona);\n            } else {\n                throw new Error(result.error || \"激活人设失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"激活人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 刷新当前人设\n    const refreshPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await initializePersona();\n    }, [\n        initializePersona\n    ]);\n    // 清除人设\n    const clearPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const previousPersona = currentPersona;\n        setCurrentPersona(null);\n        setError(null);\n        emitPersonaChange(\"clear\", undefined, previousPersona);\n    }, [\n        currentPersona,\n        emitPersonaChange\n    ]);\n    // 事件订阅\n    const onPersonaChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((callback)=>{\n        return personaEventBus.subscribe(\"*\", callback);\n    }, []);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!initialPersona) {\n            initializePersona();\n        }\n        // 清理函数\n        return ()=>{\n            personaEventBus.clear();\n        };\n    }, [\n        initialPersona,\n        initializePersona\n    ]);\n    const contextValue = {\n        currentPersona,\n        isLoading,\n        error,\n        selectPersona,\n        activatePersona,\n        refreshPersona,\n        clearPersona,\n        onPersonaChange\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonaContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\contexts\\\\PersonaContext.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PersonaProvider, \"RJ1dENDJlVoNMFuW1ZA25YfFesg=\");\n_c = PersonaProvider;\n// Hook for using PersonaContext\nconst usePersona = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PersonaContext);\n    if (!context) {\n        throw new Error(\"usePersona must be used within a PersonaProvider\");\n    }\n    return context;\n};\n_s1(usePersona, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 导出事件总线供高级用法\n\nvar _c;\n$RefreshReg$(_c, \"PersonaProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/PersonaContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/audienceService.ts":
/*!*****************************************!*\
  !*** ./src/services/audienceService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudienceService: function() { return /* binding */ AudienceService; }\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 受众管理服务\r\n * 提供受众的创建、存储、管理功能，用于AI助手的动态受众设置\r\n */ \nclass AudienceService {\n    /**\r\n   * 获取受众服务单例\r\n   */ static getInstance() {\n        if (!AudienceService.instance) {\n            AudienceService.instance = new AudienceService();\n        }\n        return AudienceService.instance;\n    }\n    /**\r\n   * 创建受众\r\n   */ async createAudience(audienceData) {\n        try {\n            const now = Date.now();\n            const audienceId = \"audience-\".concat(now, \"-\").concat(Math.random().toString(36).substring(2, 9));\n            const audience = {\n                ...audienceData,\n                id: audienceId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.storeName, audience);\n            if (result.success) {\n                console.log(\"✅ 受众创建成功:\", audienceData.name);\n                return {\n                    success: true,\n                    data: audience\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"创建受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取受众\r\n   */ async getAudience(audienceId) {\n        try {\n            const result = await this.dbService.get(this.storeName, audienceId);\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有受众\r\n   */ async getAllAudiences() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按创建时间排序，最新的在前\n                const sortedAudiences = result.data.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedAudiences\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取所有受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新受众\r\n   */ async updateAudience(audienceId, updates) {\n        try {\n            // 获取现有受众\n            const existingResult = await this.dbService.get(this.storeName, audienceId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"受众不存在\"\n                };\n            }\n            const existingAudience = existingResult.data;\n            // 合并更新\n            const updatedAudience = {\n                ...existingAudience,\n                ...updates,\n                id: audienceId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.storeName, updatedAudience);\n            if (result.success) {\n                console.log(\"✅ 受众更新成功:\", audienceId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"更新受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除受众\r\n   */ async deleteAudience(audienceId) {\n        try {\n            const result = await this.dbService.delete(this.storeName, audienceId);\n            if (result.success) {\n                console.log(\"✅ 受众删除成功:\", audienceId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"删除受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取活跃受众\r\n   */ async getActiveAudience() {\n        try {\n            // 获取所有受众，然后筛选活跃的受众\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 查找活跃受众\n                const activeAudiences = result.data.filter((audience)=>audience.isActive);\n                if (activeAudiences.length > 0) {\n                    // 如果有多个活跃受众，返回最新的一个\n                    const activeAudience = activeAudiences.sort((a, b)=>b.updatedAt - a.updatedAt)[0];\n                    return {\n                        success: true,\n                        data: activeAudience\n                    };\n                }\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = \"获取活跃受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 设置活跃受众\r\n   */ async setActiveAudience(audienceId) {\n        try {\n            // 首先将所有受众设为非活跃\n            const allAudiencesResult = await this.dbService.getAll(this.storeName);\n            if (allAudiencesResult.success && allAudiencesResult.data) {\n                for (const audience of allAudiencesResult.data){\n                    if (audience.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...audience,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 设置指定受众为活跃\n            const result = await this.updateAudience(audienceId, {\n                isActive: true\n            });\n            if (result.success) {\n                console.log(\"✅ 活跃受众设置成功:\", audienceId);\n                // 获取新激活的受众并通知监听器\n                const activeAudienceResult = await this.getAudience(audienceId);\n                if (activeAudienceResult.success && activeAudienceResult.data) {\n                    this.notifyAudienceChangeListeners(activeAudienceResult.data);\n                }\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"设置活跃受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 订阅受众变化事件\r\n   */ subscribeToAudienceChange(listener) {\n        this.audienceChangeListeners.push(listener);\n        console.log(\"✅ 受众变化监听器已添加，当前监听器数量:\", this.audienceChangeListeners.length);\n    }\n    /**\r\n   * 取消订阅受众变化事件\r\n   */ unsubscribeFromAudienceChange(listener) {\n        this.audienceChangeListeners = this.audienceChangeListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 受众变化监听器已移除，当前监听器数量:\", this.audienceChangeListeners.length);\n    }\n    /**\r\n   * 通知所有监听器受众已变化\r\n   */ notifyAudienceChangeListeners(audience) {\n        console.log(\"\\uD83D\\uDD14 通知受众变化:\", (audience === null || audience === void 0 ? void 0 : audience.name) || \"无\", \"监听器数量:\", this.audienceChangeListeners.length);\n        for (const listener of this.audienceChangeListeners){\n            try {\n                listener(audience);\n            } catch (error) {\n                console.error(\"❌ 受众变化监听器执行失败:\", error);\n            }\n        }\n    }\n    /**\r\n   * 清除活跃受众（设置所有受众为非活跃）\r\n   */ async clearActiveAudience() {\n        try {\n            // 将所有受众设为非活跃\n            const allAudiencesResult = await this.dbService.getAll(this.storeName);\n            if (allAudiencesResult.success && allAudiencesResult.data) {\n                for (const audience of allAudiencesResult.data){\n                    if (audience.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...audience,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 通知监听器受众已清除\n            this.notifyAudienceChangeListeners(null);\n            console.log(\"✅ 活跃受众已清除\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = \"清除活跃受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建默认受众\r\n   */ async createDefaultAudience() {\n        const defaultAudience = {\n            name: \"通用受众\",\n            description: \"适用于一般场景的通用受众，提供平衡的回应风格，既不过于正式也不过于随意。\",\n            isActive: true\n        };\n        const result = await this.createAudience(defaultAudience);\n        if (result.success && result.data) {\n            return {\n                success: true,\n                data: result.data.id\n            };\n        }\n        return {\n            success: false,\n            error: result.error\n        };\n    }\n    /**\r\n   * 创建默认受众配置列表\r\n   */ async createDefaultAudiences() {\n        try {\n            const defaultAudiences = [\n                {\n                    name: \"网文快餐\",\n                    description: \"面向网络文学读者，偏好快节奏、情节紧凑的内容风格，语言生动有趣，富有想象力。\",\n                    isActive: true\n                },\n                {\n                    name: \"学术研究\",\n                    description: \"面向学术研究人员，需要严谨、专业的表达方式，注重逻辑性和准确性，使用规范的学术语言。\",\n                    isActive: false\n                },\n                {\n                    name: \"商务沟通\",\n                    description: \"面向商务场景，注重效率和专业性的沟通风格，语言简洁明了，重点突出。\",\n                    isActive: false\n                },\n                {\n                    name: \"创意写作\",\n                    description: \"面向创意写作者，鼓励想象力和创新表达，语言富有感染力和艺术性。\",\n                    isActive: false\n                }\n            ];\n            const createdAudienceIds = [];\n            for (const audienceData of defaultAudiences){\n                const result = await this.createAudience(audienceData);\n                if (result.success && result.data) {\n                    createdAudienceIds.push(result.data.id);\n                }\n            }\n            console.log(\"✅ 默认受众创建完成:\", createdAudienceIds.length);\n            return {\n                success: true,\n                data: createdAudienceIds\n            };\n        } catch (error) {\n            const errorMessage = \"创建默认受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 初始化受众系统\r\n   * 创建默认受众配置，支持重试机制和数据迁移\r\n   */ async initializeAudienceSystem() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 3;\n        const startTime = Date.now();\n        console.log(\"\\uD83D\\uDE80 开始初始化受众系统...\");\n        try {\n            // 步骤1: 检查数据库连接\n            await this.ensureDatabaseConnection();\n            // 步骤2: 检查是否已经初始化\n            const existingAudiencesResult = await this.getAllAudiences();\n            if (existingAudiencesResult.success && existingAudiencesResult.data && existingAudiencesResult.data.length > 0) {\n                var _existingAudiencesResult_data_;\n                console.log(\"✅ 受众系统已初始化，发现\", existingAudiencesResult.data.length, \"个受众\");\n                // 检查是否有激活的受众\n                const activeAudience = existingAudiencesResult.data.find((audience)=>audience.isActive);\n                if (!activeAudience && existingAudiencesResult.data.length > 0) {\n                    // 如果没有激活的受众，激活第一个\n                    const firstAudience = existingAudiencesResult.data[0];\n                    await this.setActiveAudience(firstAudience.id);\n                    console.log(\"✅ 自动激活第一个受众:\", firstAudience.name);\n                }\n                const defaultAudienceId = (activeAudience === null || activeAudience === void 0 ? void 0 : activeAudience.id) || ((_existingAudiencesResult_data_ = existingAudiencesResult.data[0]) === null || _existingAudiencesResult_data_ === void 0 ? void 0 : _existingAudiencesResult_data_.id);\n                return {\n                    success: true,\n                    defaultAudienceId,\n                    isFirstTime: false,\n                    initializationTime: Date.now() - startTime\n                };\n            }\n            // 步骤3: 执行数据迁移检查\n            await this.performDataMigration();\n            // 步骤4: 创建默认受众配置\n            console.log(\"\\uD83D\\uDCDD 创建默认受众配置...\");\n            const defaultAudiencesResult = await this.createDefaultAudiencesWithRetry(retryCount);\n            if (!defaultAudiencesResult.success || !defaultAudiencesResult.data) {\n                throw new Error(defaultAudiencesResult.error || \"创建默认受众失败\");\n            }\n            const createdAudienceIds = defaultAudiencesResult.data;\n            const defaultAudienceId = createdAudienceIds.length > 0 ? createdAudienceIds[0] : undefined;\n            // 步骤5: 验证初始化结果\n            await this.validateInitialization();\n            const initializationTime = Date.now() - startTime;\n            console.log(\"✅ 受众系统初始化完成，耗时:\", initializationTime, \"ms\");\n            return {\n                success: true,\n                defaultAudienceId,\n                createdAudiences: createdAudienceIds,\n                isFirstTime: true,\n                initializationTime\n            };\n        } catch (error) {\n            const errorMessage = \"初始化受众系统失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            // 如果还有重试次数，进行重试\n            if (retryCount > 0) {\n                console.log(\"\\uD83D\\uDD04 重试初始化受众系统，剩余重试次数: \".concat(retryCount - 1));\n                await this.delay(1000); // 等待1秒后重试\n                return this.initializeAudienceSystem(retryCount - 1);\n            }\n            return {\n                success: false,\n                error: errorMessage,\n                initializationTime: Date.now() - startTime\n            };\n        }\n    }\n    /**\r\n   * 确保数据库连接正常\r\n   */ async ensureDatabaseConnection() {\n        try {\n            // 尝试执行一个简单的数据库操作来测试连接\n            await this.dbService.getAll(this.storeName);\n            console.log(\"✅ 数据库连接正常\");\n        } catch (error) {\n            console.error(\"❌ 数据库连接失败:\", error);\n            throw new Error(\"数据库连接失败，请检查IndexedDB支持\");\n        }\n    }\n    /**\r\n   * 执行数据迁移\r\n   */ async performDataMigration() {\n        try {\n            console.log(\"\\uD83D\\uDD04 检查数据迁移需求...\");\n            // 获取当前数据版本\n            const versionKey = \"audience_data_version\";\n            const currentVersion = localStorage.getItem(versionKey) || \"1.0.0\";\n            const targetVersion = \"1.0.0\";\n            if (currentVersion !== targetVersion) {\n                console.log(\"\\uD83D\\uDD04 执行数据迁移: \".concat(currentVersion, \" -> \").concat(targetVersion));\n                // 这里可以添加具体的迁移逻辑\n                // 例如：更新数据结构、添加新字段等\n                // 更新版本号\n                localStorage.setItem(versionKey, targetVersion);\n                console.log(\"✅ 数据迁移完成\");\n            } else {\n                console.log(\"✅ 数据版本匹配，无需迁移\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ 数据迁移检查失败:\", error);\n        // 迁移失败不应该阻止初始化过程\n        }\n    }\n    /**\r\n   * 带重试机制的默认受众创建\r\n   */ async createDefaultAudiencesWithRetry(retryCount) {\n        try {\n            return await this.createDefaultAudiences();\n        } catch (error) {\n            if (retryCount > 0) {\n                console.log(\"\\uD83D\\uDD04 重试创建默认受众，剩余重试次数: \".concat(retryCount - 1));\n                await this.delay(500);\n                return this.createDefaultAudiencesWithRetry(retryCount - 1);\n            }\n            throw error;\n        }\n    }\n    /**\r\n   * 验证初始化结果\r\n   */ async validateInitialization() {\n        try {\n            console.log(\"\\uD83D\\uDD0D 验证初始化结果...\");\n            // 检查受众是否创建成功\n            const audiencesResult = await this.getAllAudiences();\n            if (!audiencesResult.success || !audiencesResult.data || audiencesResult.data.length === 0) {\n                throw new Error(\"验证失败：未找到任何受众\");\n            }\n            // 检查是否有激活的受众\n            const activeAudience = audiencesResult.data.find((audience)=>audience.isActive);\n            if (!activeAudience) {\n                throw new Error(\"验证失败：没有激活的受众\");\n            }\n            console.log(\"✅ 初始化验证通过，共\", audiencesResult.data.length, \"个受众，激活受众:\", activeAudience.name);\n        } catch (error) {\n            console.error(\"❌ 初始化验证失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 延迟函数\r\n   */ delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    /**\r\n   * 清空所有受众\r\n   */ async clearAllAudiences() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                console.log(\"✅ 所有受众已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"清空受众失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    constructor(){\n        this.storeName = \"audiences\";\n        // 受众变化事件监听器\n        this.audienceChangeListeners = [];\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/audienceService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/database/DatabaseService.ts":
/*!**************************************************!*\
  !*** ./src/services/database/DatabaseService.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: function() { return /* binding */ DatabaseService; }\n/* harmony export */ });\n/**\r\n * IndexedDB 数据库服务\r\n * 提供底层数据库操作和连接管理\r\n */ class DatabaseService {\n    /**\r\n   * 获取数据库服务单例\r\n   */ static getInstance() {\n        if (!DatabaseService.instance) {\n            DatabaseService.instance = new DatabaseService();\n        }\n        return DatabaseService.instance;\n    }\n    /**\r\n   * 初始化数据库连接\r\n   */ async initialize() {\n        try {\n            if (this.db) {\n                return {\n                    success: true,\n                    data: this.db\n                };\n            }\n            const db = await this.openDatabase();\n            this.db = db;\n            console.log(\"✅ IndexedDB 数据库初始化成功\");\n            return {\n                success: true,\n                data: db\n            };\n        } catch (error) {\n            const errorMessage = \"数据库初始化失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 打开数据库连接\r\n   */ openDatabase() {\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.config.name, this.config.version);\n            request.onerror = ()=>{\n                var _request_error;\n                reject(new Error(\"无法打开数据库: \".concat((_request_error = request.error) === null || _request_error === void 0 ? void 0 : _request_error.message)));\n            };\n            request.onsuccess = ()=>{\n                resolve(request.result);\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                const transaction = event.target.transaction;\n                const oldVersion = event.oldVersion;\n                const newVersion = event.newVersion;\n                console.log(\"\\uD83D\\uDD04 数据库升级: \".concat(oldVersion, \" -> \").concat(newVersion));\n                this.createObjectStores(db, transaction, oldVersion);\n            };\n        });\n    }\n    /**\r\n   * 创建对象存储和索引（简化版本）\r\n   */ createObjectStores(db, transaction, oldVersion) {\n        console.log(\"\\uD83D\\uDD04 开始数据库升级...\");\n        console.log(\"\\uD83D\\uDCCA 升级信息: 旧版本 \".concat(oldVersion, \" -> 新版本 \").concat(this.config.version));\n        try {\n            // 简化策略：直接创建缺失的存储，不删除现有存储\n            Object.entries(this.config.stores).forEach((param)=>{\n                let [storeName, storeConfig] = param;\n                if (!db.objectStoreNames.contains(storeName)) {\n                    console.log(\"\\uD83D\\uDCE6 创建新存储: \".concat(storeName));\n                    // 创建对象存储\n                    const store = db.createObjectStore(storeName, {\n                        keyPath: storeConfig.keyPath\n                    });\n                    // 创建索引\n                    if (storeConfig.indexes) {\n                        Object.entries(storeConfig.indexes).forEach((param)=>{\n                            let [indexName, indexConfig] = param;\n                            try {\n                                store.createIndex(indexName, indexConfig.keyPath, {\n                                    unique: indexConfig.unique || false\n                                });\n                                console.log(\"  ✅ 创建索引: \".concat(storeName, \".\").concat(indexName));\n                            } catch (error) {\n                                console.warn(\"  ⚠️ 索引创建失败: \".concat(storeName, \".\").concat(indexName), error);\n                            }\n                        });\n                    }\n                } else {\n                    console.log(\"✅ 存储已存在: \".concat(storeName));\n                }\n            });\n            console.log(\"\\uD83C\\uDF89 数据库升级完成！\");\n        } catch (error) {\n            console.error(\"❌ 数据库升级失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 重新创建存储并恢复数据\r\n   */ recreateStore(db, storeName, storeConfig, backupData) {\n        console.log(\"\\uD83D\\uDCE6 重新创建存储: \".concat(storeName));\n        // 创建新存储\n        const store = db.createObjectStore(storeName, {\n            keyPath: storeConfig.keyPath\n        });\n        // 创建所有索引\n        if (storeConfig.indexes) {\n            Object.entries(storeConfig.indexes).forEach((param)=>{\n                let [indexName, indexConfig] = param;\n                try {\n                    store.createIndex(indexName, indexConfig.keyPath, {\n                        unique: indexConfig.unique || false\n                    });\n                    console.log(\"  ✅ 创建索引: \".concat(storeName, \".\").concat(indexName));\n                } catch (error) {\n                    console.warn(\"  ⚠️ 索引创建失败: \".concat(storeName, \".\").concat(indexName), error);\n                }\n            });\n        }\n        // 如果有备份数据，立即恢复\n        if (backupData && backupData.length > 0) {\n            console.log(\"\\uD83D\\uDCE5 恢复数据到 \".concat(storeName, \": \").concat(backupData.length, \" 条记录\"));\n            let restored = 0;\n            backupData.forEach((item)=>{\n                try {\n                    const request = store.add(item);\n                    request.onsuccess = ()=>{\n                        restored++;\n                        if (restored === backupData.length) {\n                            console.log(\"✅ \".concat(storeName, \" 数据恢复完成: \").concat(restored, \" 条记录\"));\n                        }\n                    };\n                    request.onerror = ()=>{\n                        console.warn(\"⚠️ 恢复单条数据失败:\", item);\n                    };\n                } catch (error) {\n                    console.warn(\"⚠️ 添加数据失败:\", error);\n                }\n            });\n        }\n    }\n    /**\r\n   * 获取数据库连接\r\n   */ async getDatabase() {\n        if (!this.db) {\n            const result = await this.initialize();\n            if (!result.success || !result.data) {\n                throw new Error(result.error || \"数据库连接失败\");\n            }\n        }\n        return this.db;\n    }\n    /**\r\n   * 执行事务操作\r\n   */ async executeTransaction(storeNames, mode, operation) {\n        try {\n            const db = await this.getDatabase();\n            const transaction = db.transaction(storeNames, mode);\n            // 设置事务错误处理\n            transaction.onerror = ()=>{\n                console.error(\"事务执行失败:\", transaction.error);\n            };\n            // 获取对象存储\n            const stores = Array.isArray(storeNames) ? storeNames.map((name)=>transaction.objectStore(name)) : transaction.objectStore(storeNames);\n            const result = await operation(stores);\n            return {\n                success: true,\n                data: result\n            };\n        } catch (error) {\n            const errorMessage = \"事务执行失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 添加数据\r\n   */ async add(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.add(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 获取数据\r\n   */ async get(storeName, key) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.get(key);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 获取所有数据\r\n   */ async getAll(storeName) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.getAll();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 更新数据\r\n   */ async update(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.put(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 保存或更新数据 (put操作)\r\n   */ async put(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.put(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 删除数据\r\n   */ async delete(storeName, key) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.delete(key);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve();\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 通过索引查询数据\r\n   */ async getByIndex(storeName, indexName, value) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const index = store.index(indexName);\n            const request = index.getAll(value);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 计数\r\n   */ async count(storeName) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.count();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 清空存储\r\n   */ async clear(storeName) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.clear();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve();\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 关闭数据库连接\r\n   */ close() {\n        if (this.db) {\n            this.db.close();\n            this.db = null;\n            console.log(\"\\uD83D\\uDD12 数据库连接已关闭\");\n        }\n    }\n    /**\r\n   * 检查浏览器是否支持IndexedDB\r\n   */ static isSupported() {\n        return \"indexedDB\" in window;\n    }\n    constructor(){\n        this.db = null;\n        this.config = DatabaseService.DB_CONFIG;\n    }\n}\n// 数据库配置\nDatabaseService.DB_CONFIG = {\n    name: \"ArtworkPlatformDB\",\n    version: 13,\n    stores: {\n        artworks: {\n            keyPath: \"id\",\n            indexes: {\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                },\n                tags: {\n                    keyPath: \"tags\",\n                    unique: false\n                }\n            }\n        },\n        fonts: {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                family: {\n                    keyPath: \"family\"\n                },\n                uploadedAt: {\n                    keyPath: \"uploadedAt\"\n                }\n            }\n        },\n        config: {\n            keyPath: \"id\",\n            indexes: {\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"font-configs\": {\n            keyPath: \"id\",\n            indexes: {\n                fontId: {\n                    keyPath: \"fontId\"\n                },\n                appliedAt: {\n                    keyPath: \"appliedAt\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                }\n            }\n        },\n        \"font-history\": {\n            keyPath: \"id\",\n            indexes: {\n                fontId: {\n                    keyPath: \"fontId\"\n                },\n                appliedAt: {\n                    keyPath: \"appliedAt\"\n                },\n                fontFamily: {\n                    keyPath: \"fontFamily\"\n                }\n            }\n        },\n        \"editor-files\": {\n            keyPath: \"id\",\n            indexes: {\n                artworkId: {\n                    keyPath: \"artworkId\"\n                },\n                parentId: {\n                    keyPath: \"parentId\"\n                },\n                path: {\n                    keyPath: \"path\"\n                },\n                type: {\n                    keyPath: \"type\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"editor-settings\": {\n            keyPath: \"id\",\n            indexes: {\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"ai-configs\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"ai-templates\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                category: {\n                    keyPath: \"category\"\n                },\n                isBuiltIn: {\n                    keyPath: \"isBuiltIn\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"ai-history\": {\n            keyPath: \"id\",\n            indexes: {\n                artworkId: {\n                    keyPath: \"artworkId\"\n                },\n                timestamp: {\n                    keyPath: \"timestamp\"\n                },\n                model: {\n                    keyPath: \"model\"\n                }\n            }\n        },\n        \"personas\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                folderId: {\n                    keyPath: \"folderId\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"persona_folders\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"audiences\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"chat-sessions\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                }\n            }\n        },\n        \"chat-messages\": {\n            keyPath: \"id\",\n            indexes: {\n                sessionId: {\n                    keyPath: \"sessionId\"\n                },\n                type: {\n                    keyPath: \"type\"\n                },\n                timestamp: {\n                    keyPath: \"timestamp\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                }\n            }\n        },\n        \"message-tokens\": {\n            keyPath: \"id\",\n            indexes: {\n                fileId: {\n                    keyPath: \"fileId\"\n                },\n                segmentId: {\n                    keyPath: \"segmentId\"\n                },\n                sequence: {\n                    keyPath: \"sequence\"\n                },\n                messageType: {\n                    keyPath: \"messageType\"\n                },\n                language: {\n                    keyPath: \"language\"\n                },\n                timestamp: {\n                    keyPath: \"timestamp\"\n                },\n                createdAt: {\n                    keyPath: \"timestamp\"\n                }\n            }\n        },\n        \"message-collections\": {\n            keyPath: \"id\",\n            indexes: {\n                fileId: {\n                    keyPath: \"fileId\"\n                },\n                fileName: {\n                    keyPath: \"fileName\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                },\n                version: {\n                    keyPath: \"version\"\n                }\n            }\n        },\n        \"text-segments\": {\n            keyPath: \"id\",\n            indexes: {\n                fileId: {\n                    keyPath: \"fileId\"\n                },\n                language: {\n                    keyPath: \"language\"\n                },\n                wordCount: {\n                    keyPath: \"wordCount\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                }\n            }\n        },\n        \"file-associations\": {\n            keyPath: \"id\",\n            indexes: {\n                fileId: {\n                    keyPath: \"fileId\"\n                },\n                fileName: {\n                    keyPath: \"fileName\"\n                },\n                fileType: {\n                    keyPath: \"fileType\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"media-files\": {\n            keyPath: \"id\",\n            indexes: {\n                artworkId: {\n                    keyPath: \"artworkId\"\n                },\n                type: {\n                    keyPath: \"type\"\n                },\n                mimeType: {\n                    keyPath: \"mimeType\"\n                },\n                uploadedAt: {\n                    keyPath: \"uploadedAt\"\n                },\n                size: {\n                    keyPath: \"size\"\n                },\n                name: {\n                    keyPath: \"name\"\n                }\n            }\n        },\n        \"media-chunks\": {\n            keyPath: \"id\",\n            indexes: {\n                fileId: {\n                    keyPath: \"fileId\"\n                },\n                chunkIndex: {\n                    keyPath: \"chunkIndex\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                }\n            }\n        },\n        \"prompt-templates\": {\n            keyPath: \"id\",\n            indexes: {\n                content: {\n                    keyPath: \"content\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        },\n        \"prompt-configs\": {\n            keyPath: \"id\",\n            indexes: {\n                name: {\n                    keyPath: \"name\"\n                },\n                isActive: {\n                    keyPath: \"isActive\"\n                },\n                createdAt: {\n                    keyPath: \"createdAt\"\n                },\n                updatedAt: {\n                    keyPath: \"updatedAt\"\n                }\n            }\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/database/DatabaseService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/personaService.ts":
/*!****************************************!*\
  !*** ./src/services/personaService.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PersonaService: function() { return /* binding */ PersonaService; }\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 人设管理服务\r\n * 提供人设的创建、存储、管理和变量替换功能\r\n */ \nclass PersonaService {\n    /**\r\n   * 获取人设服务单例\r\n   */ static getInstance() {\n        if (!PersonaService.instance) {\n            PersonaService.instance = new PersonaService();\n        }\n        return PersonaService.instance;\n    }\n    /**\r\n   * 创建人设\r\n   */ async createPersona(personaData) {\n        try {\n            const now = Date.now();\n            const personaId = \"persona-\".concat(now, \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const persona = {\n                ...personaData,\n                id: personaId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.storeName, persona);\n            if (result.success) {\n                console.log(\"✅ 人设创建成功:\", personaData.name);\n                return {\n                    success: true,\n                    data: persona\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"创建人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取人设\r\n   */ async getPersona(personaId) {\n        try {\n            const result = await this.dbService.get(this.storeName, personaId);\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有人设\r\n   */ async getAllPersonas() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按创建时间排序，最新的在前\n                const sortedPersonas = result.data.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedPersonas\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取所有人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新人设\r\n   */ async updatePersona(personaId, updates) {\n        try {\n            // 获取现有人设\n            const existingResult = await this.dbService.get(this.storeName, personaId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"人设不存在\"\n                };\n            }\n            const existingPersona = existingResult.data;\n            // 合并更新\n            const updatedPersona = {\n                ...existingPersona,\n                ...updates,\n                id: personaId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.storeName, updatedPersona);\n            if (result.success) {\n                console.log(\"✅ 人设更新成功:\", personaId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"更新人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除人设\r\n   */ async deletePersona(personaId) {\n        try {\n            const result = await this.dbService.delete(this.storeName, personaId);\n            if (result.success) {\n                console.log(\"✅ 人设删除成功:\", personaId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"删除人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取活跃人设\r\n   */ async getActivePersona() {\n        try {\n            // 获取所有人设，然后筛选活跃的人设\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 查找活跃人设\n                const activePersonas = result.data.filter((persona)=>persona.isActive);\n                if (activePersonas.length > 0) {\n                    // 如果有多个活跃人设，返回最新的一个\n                    const activePersona = activePersonas.sort((a, b)=>b.updatedAt - a.updatedAt)[0];\n                    return {\n                        success: true,\n                        data: activePersona\n                    };\n                }\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = \"获取活跃人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 设置活跃人设\r\n   */ async setActivePersona(personaId) {\n        try {\n            // 首先将所有人设设为非活跃\n            const allPersonasResult = await this.dbService.getAll(this.storeName);\n            if (allPersonasResult.success && allPersonasResult.data) {\n                for (const persona of allPersonasResult.data){\n                    if (persona.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...persona,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 设置指定人设为活跃\n            const result = await this.updatePersona(personaId, {\n                isActive: true\n            });\n            if (result.success) {\n                console.log(\"✅ 活跃人设设置成功:\", personaId);\n                // 获取新激活的人设并通知监听器\n                const activePersonaResult = await this.getPersona(personaId);\n                if (activePersonaResult.success && activePersonaResult.data) {\n                    this.notifyPersonaChangeListeners(activePersonaResult.data);\n                }\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"设置活跃人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 订阅人设变化事件\r\n   */ subscribeToPersonaChange(listener) {\n        this.personaChangeListeners.push(listener);\n        console.log(\"✅ 人设变化监听器已添加，当前监听器数量:\", this.personaChangeListeners.length);\n    }\n    /**\r\n   * 取消订阅人设变化事件\r\n   */ unsubscribeFromPersonaChange(listener) {\n        this.personaChangeListeners = this.personaChangeListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 人设变化监听器已移除，当前监听器数量:\", this.personaChangeListeners.length);\n    }\n    /**\r\n   * 通知所有监听器人设已变化\r\n   */ notifyPersonaChangeListeners(persona) {\n        console.log(\"\\uD83D\\uDD14 通知人设变化:\", (persona === null || persona === void 0 ? void 0 : persona.name) || \"无\", \"监听器数量:\", this.personaChangeListeners.length);\n        for (const listener of this.personaChangeListeners){\n            try {\n                listener(persona);\n            } catch (error) {\n                console.error(\"❌ 人设变化监听器执行失败:\", error);\n            }\n        }\n    }\n    /**\r\n   * 清除活跃人设（设置所有人设为非活跃）\r\n   */ async clearActivePersona() {\n        try {\n            // 将所有人设设为非活跃\n            const allPersonasResult = await this.dbService.getAll(this.storeName);\n            if (allPersonasResult.success && allPersonasResult.data) {\n                for (const persona of allPersonasResult.data){\n                    if (persona.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...persona,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 通知监听器人设已清除\n            this.notifyPersonaChangeListeners(null);\n            console.log(\"✅ 活跃人设已清除\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = \"清除活跃人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取人设介绍的前100字（用于变量替换）\r\n   */ getPersonaIntroFirst100Chars(persona) {\n        if (!persona.description) return \"\";\n        return persona.description.substring(0, 100);\n    }\n    /**\r\n   * 替换设计提示词中的变量\r\n   */ replaceDesignPromptVariables(designPrompt, persona) {\n        const personaIntro = this.getPersonaIntroFirst100Chars(persona);\n        return designPrompt.replace(/\\[\\$\\{personaIntro\\}\\]/g, personaIntro);\n    }\n    /**\r\n   * 获取完整的设计提示词（包含变量替换）\r\n   */ async getDesignPromptWithPersona(designPromptTemplate) {\n        try {\n            const activePersonaResult = await this.getActivePersona();\n            if (activePersonaResult.success && activePersonaResult.data) {\n                return this.replaceDesignPromptVariables(designPromptTemplate, activePersonaResult.data);\n            }\n            // 如果没有活跃人设，返回原始模板\n            return designPromptTemplate;\n        } catch (error) {\n            console.error(\"❌ 获取设计提示词失败:\", error);\n            return designPromptTemplate;\n        }\n    }\n    /**\r\n   * 创建默认人设\r\n   */ async createDefaultPersona(folderId) {\n        const defaultPersona = {\n            name: \"默认助手\",\n            description: \"我是一个智能助手，专注于为用户提供高质量的帮助和支持。我具有专业的知识背景，能够理解用户的需求并提供相应的解决方案。我的性格友好、专业、乐于助人。作为一个智能助手，我会尽力帮助用户解决问题，提供准确和有用的信息。\",\n            folderId: folderId || \"default-folder\",\n            isActive: true\n        };\n        const result = await this.createPersona(defaultPersona);\n        if (result.success && result.data) {\n            return {\n                success: true,\n                data: result.data.id\n            };\n        }\n        return {\n            success: false,\n            error: result.error\n        };\n    }\n    /**\r\n   * 清空所有人设\r\n   */ async clearAllPersonas() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                console.log(\"✅ 所有人设已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"清空人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 迁移人设数据\r\n   * 将现有的复杂字段内容合并到description字段中\r\n   */ async migratePersonaData() {\n        try {\n            console.log(\"\\uD83D\\uDD04 开始迁移人设数据...\");\n            const result = await this.getAllPersonas();\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: \"无法获取现有人设数据\"\n                };\n            }\n            let migratedCount = 0;\n            let skippedCount = 0;\n            for (const persona of result.data){\n                // 检查是否需要迁移（是否包含旧字段）\n                const personaAny = persona;\n                if (this.needsMigration(personaAny)) {\n                    const newDescription = this.mergePersonaFields(personaAny);\n                    // 更新人设数据\n                    const updateResult = await this.updatePersona(persona.id, {\n                        description: newDescription\n                    });\n                    if (updateResult.success) {\n                        migratedCount++;\n                        console.log(\"✅ 已迁移人设: \".concat(persona.name));\n                    } else {\n                        console.error(\"❌ 迁移失败: \".concat(persona.name), updateResult.error);\n                    }\n                } else {\n                    skippedCount++;\n                }\n            }\n            console.log(\"\\uD83C\\uDF89 人设数据迁移完成: 迁移 \".concat(migratedCount, \" 个，跳过 \").concat(skippedCount, \" 个\"));\n            return {\n                success: true,\n                data: {\n                    migratedCount,\n                    skippedCount\n                }\n            };\n        } catch (error) {\n            const errorMessage = \"迁移人设数据失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 检查人设是否需要迁移\r\n   */ needsMigration(persona) {\n        return !!(persona.role || persona.personality || persona.instructions || persona.personaIntro);\n    }\n    /**\r\n   * 合并人设字段到description中\r\n   */ mergePersonaFields(persona) {\n        let description = persona.description || \"\";\n        // 如果已有description，先保留原有内容\n        if (description) {\n            description += \"\\n\\n\";\n        }\n        // 按逻辑顺序合并字段内容\n        const sections = [];\n        if (persona.role) {\n            sections.push(\"**角色定位**: \".concat(persona.role));\n        }\n        if (persona.personaIntro) {\n            sections.push(\"**人设介绍**: \".concat(persona.personaIntro));\n        }\n        if (persona.personality) {\n            sections.push(\"**性格特征**: \".concat(persona.personality));\n        }\n        if (persona.instructions) {\n            sections.push(\"**行为指令**: \".concat(persona.instructions));\n        }\n        // 将所有部分合并\n        description += sections.join(\"\\n\\n\");\n        return description.trim();\n    }\n    /**\r\n   * 获取迁移状态\r\n   * 检查是否还有需要迁移的数据\r\n   */ async getMigrationStatus() {\n        try {\n            const result = await this.getAllPersonas();\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: \"无法获取人设数据\"\n                };\n            }\n            const totalCount = result.data.length;\n            const needsMigrationCount = result.data.filter((persona)=>this.needsMigration(persona)).length;\n            return {\n                success: true,\n                data: {\n                    needsMigration: needsMigrationCount > 0,\n                    totalCount,\n                    needsMigrationCount\n                }\n            };\n        } catch (error) {\n            const errorMessage = \"获取迁移状态失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建人设文件夹\r\n   */ async createFolder(folderData) {\n        try {\n            const now = Date.now();\n            const folderId = \"folder-\".concat(now, \"-\").concat(Math.random().toString(36).substring(2, 9));\n            const folder = {\n                ...folderData,\n                id: folderId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.folderStoreName, folder);\n            if (result.success) {\n                console.log(\"✅ 人设文件夹创建成功:\", folderData.name);\n                return {\n                    success: true,\n                    data: folder\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"创建文件夹失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有文件夹\r\n   */ async getAllFolders() {\n        try {\n            const result = await this.dbService.getAll(this.folderStoreName);\n            if (result.success && result.data) {\n                // 为每个文件夹计算人设数量\n                const foldersWithCount = await Promise.all(result.data.map(async (folder)=>{\n                    const personaCountResult = await this.getPersonaCountInFolder(folder.id);\n                    return {\n                        ...folder,\n                        personaCount: personaCountResult.success ? personaCountResult.data : 0\n                    };\n                }));\n                // 按创建时间排序，最新的在前\n                const sortedFolders = foldersWithCount.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedFolders\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取所有文件夹失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取文件夹中的人设数量\r\n   */ async getPersonaCountInFolder(folderId) {\n        try {\n            const result = await this.getPersonasByFolder(folderId);\n            if (result.success && result.data) {\n                return {\n                    success: true,\n                    data: result.data.length\n                };\n            }\n            return {\n                success: true,\n                data: 0\n            };\n        } catch (error) {\n            const errorMessage = \"获取文件夹人设数量失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取指定文件夹中的所有人设\r\n   */ async getPersonasByFolder(folderId) {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 筛选指定文件夹的人设\n                const folderPersonas = result.data.filter((persona)=>persona.folderId === folderId);\n                // 按创建时间排序，最新的在前\n                const sortedPersonas = folderPersonas.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedPersonas\n                };\n            }\n            return {\n                success: true,\n                data: []\n            };\n        } catch (error) {\n            const errorMessage = \"获取文件夹人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新文件夹\r\n   */ async updateFolder(folderId, updates) {\n        try {\n            // 获取现有文件夹\n            const existingResult = await this.dbService.get(this.folderStoreName, folderId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"文件夹不存在\"\n                };\n            }\n            const existingFolder = existingResult.data;\n            // 合并更新\n            const updatedFolder = {\n                ...existingFolder,\n                ...updates,\n                id: folderId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.folderStoreName, updatedFolder);\n            if (result.success) {\n                console.log(\"✅ 文件夹更新成功:\", folderId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"更新文件夹失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除文件夹\r\n   */ async deleteFolder(folderId) {\n        try {\n            // 检查文件夹中是否还有人设\n            const personasResult = await this.getPersonasByFolder(folderId);\n            if (personasResult.success && personasResult.data && personasResult.data.length > 0) {\n                return {\n                    success: false,\n                    error: \"文件夹中还有人设，无法删除\"\n                };\n            }\n            const result = await this.dbService.delete(this.folderStoreName, folderId);\n            if (result.success) {\n                console.log(\"✅ 文件夹删除成功:\", folderId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"删除文件夹失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 移动人设到指定文件夹\r\n   */ async movePersonaToFolder(personaId, targetFolderId) {\n        try {\n            // 验证目标文件夹是否存在\n            const folderResult = await this.dbService.get(this.folderStoreName, targetFolderId);\n            if (!folderResult.success || !folderResult.data) {\n                return {\n                    success: false,\n                    error: \"目标文件夹不存在\"\n                };\n            }\n            // 更新人设的文件夹ID\n            const result = await this.updatePersona(personaId, {\n                folderId: targetFolderId\n            });\n            if (result.success) {\n                console.log(\"✅ 人设移动成功:\", personaId, \"->\", targetFolderId);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"移动人设失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建默认文件夹\r\n   */ async createDefaultFolders() {\n        try {\n            const defaultFolders = [\n                {\n                    name: \"工作助手\"\n                },\n                {\n                    name: \"学习助手\"\n                },\n                {\n                    name: \"生活助手\"\n                },\n                {\n                    name: \"创意助手\"\n                }\n            ];\n            const createdFolderIds = [];\n            for (const folderData of defaultFolders){\n                const result = await this.createFolder(folderData);\n                if (result.success && result.data) {\n                    createdFolderIds.push(result.data.id);\n                }\n            }\n            console.log(\"✅ 默认文件夹创建完成:\", createdFolderIds.length);\n            return {\n                success: true,\n                data: createdFolderIds\n            };\n        } catch (error) {\n            const errorMessage = \"创建默认文件夹失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 初始化人设系统\r\n   * 创建默认文件夹和默认人设\r\n   */ async initializePersonaSystem() {\n        try {\n            // 检查是否已经初始化\n            const foldersResult = await this.getAllFolders();\n            if (foldersResult.success && foldersResult.data && foldersResult.data.length > 0) {\n                console.log(\"✅ 人设系统已初始化\");\n                // 系统已初始化，获取第一个文件夹和第一个人设的ID\n                const firstFolder = foldersResult.data[0];\n                const personasResult = await this.getPersonasByFolder(firstFolder.id);\n                const firstPersonaId = personasResult.success && personasResult.data && personasResult.data.length > 0 ? personasResult.data[0].id : \"no-persona\";\n                return {\n                    success: true,\n                    data: {\n                        folderId: firstFolder.id,\n                        personaId: firstPersonaId\n                    }\n                };\n            }\n            // 创建默认文件夹\n            const defaultFolderResult = await this.createFolder({\n                name: \"默认助手\"\n            });\n            if (!defaultFolderResult.success || !defaultFolderResult.data) {\n                return {\n                    success: false,\n                    error: \"创建默认文件夹失败\"\n                };\n            }\n            const defaultFolderId = defaultFolderResult.data.id;\n            // 创建默认人设\n            const defaultPersona = {\n                name: \"默认助手\",\n                description: \"我是一个智能助手，专注于为用户提供高质量的帮助和支持。我具有专业的知识背景，能够理解用户的需求并提供相应的解决方案。我的性格友好、专业、乐于助人。作为一个智能助手，我会尽力帮助用户解决问题，提供准确和有用的信息。\",\n                folderId: defaultFolderId,\n                isActive: true\n            };\n            const personaResult = await this.createPersona(defaultPersona);\n            if (!personaResult.success || !personaResult.data) {\n                return {\n                    success: false,\n                    error: \"创建默认人设失败\"\n                };\n            }\n            console.log(\"✅ 人设系统初始化完成\");\n            return {\n                success: true,\n                data: {\n                    folderId: defaultFolderId,\n                    personaId: personaResult.data.id\n                }\n            };\n        } catch (error) {\n            const errorMessage = \"初始化人设系统失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    constructor(){\n        this.storeName = \"personas\";\n        // 人设变化事件监听器\n        this.personaChangeListeners = [];\n        // ==================== 文件夹管理功能 ====================\n        this.folderStoreName = \"persona_folders\";\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/personaService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/Zjc0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["css-src_app_globals_css-src_styles_animations_css-src_styles_colors_css-src_styles_modal-anim-5efe5b","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);